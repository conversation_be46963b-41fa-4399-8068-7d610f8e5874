import * as admin from "firebase-admin";
import { UserEntity, Withdrawal24h } from "../types";
import { log } from "../utils/logger";

const HOURS_24_IN_MS = 24 * 60 * 60 * 1000;

export interface WithdrawalLimitInfo {
  currentWithdrawn: number;
  remainingLimit: number;
  canWithdraw: boolean;
  resetAt: Date;
}

export interface CheckWithdrawalLimitParams {
  userId: string;
  requestedAmount: number;
  maxWithdrawalAmount: number;
}

export interface UpdateWithdrawalTrackingParams {
  userId: string;
  withdrawnAmount: number;
}

/**
 * Checks if the user can withdraw the requested amount within their 24-hour limit
 */
export async function checkWithdrawalLimit(
  params: CheckWithdrawalLimitParams
): Promise<WithdrawalLimitInfo> {
  const { userId, requestedAmount, maxWithdrawalAmount } = params;

  try {
    const db = admin.firestore();
    const userDoc = await db.collection("users").doc(userId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const userData = userDoc.data() as UserEntity;
    const now = admin.firestore.Timestamp.now();
    const currentTime = now.toDate();

    let currentWithdrawn = 0;
    let resetAt = new Date(currentTime.getTime() + HOURS_24_IN_MS);

    // Check if user has withdrawal tracking data
    if (userData.withdrawal_24h) {
      const lastResetTime = userData.withdrawal_24h.lastResetAt.toDate();
      const timeSinceReset = currentTime.getTime() - lastResetTime.getTime();

      // If less than 24 hours have passed since last reset, use existing data
      if (timeSinceReset < HOURS_24_IN_MS) {
        currentWithdrawn = userData.withdrawal_24h.amount;
        resetAt = new Date(lastResetTime.getTime() + HOURS_24_IN_MS);
      }
      // If 24+ hours have passed, the limit has reset (currentWithdrawn stays 0)
    }

    const remainingLimit = Math.max(0, maxWithdrawalAmount - currentWithdrawn);
    const canWithdraw = requestedAmount <= remainingLimit;

    const limitInfo: WithdrawalLimitInfo = {
      currentWithdrawn,
      remainingLimit,
      canWithdraw,
      resetAt,
    };

    log.info("Withdrawal limit check completed", {
      userId,
      requestedAmount,
      maxWithdrawalAmount,
      currentWithdrawn,
      remainingLimit,
      canWithdraw,
      operation: "check_withdrawal_limit",
    });

    return limitInfo;
  } catch (error) {
    log.error("Error checking withdrawal limit", error, {
      userId,
      requestedAmount,
      maxWithdrawalAmount,
      operation: "check_withdrawal_limit",
    });
    throw error;
  }
}

/**
 * Updates the user's 24-hour withdrawal tracking after a successful withdrawal
 */
export async function updateWithdrawalTracking(
  params: UpdateWithdrawalTrackingParams
): Promise<void> {
  const { userId, withdrawnAmount } = params;

  try {
    const db = admin.firestore();
    const userRef = db.collection("users").doc(userId);

    await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);

      if (!userDoc.exists) {
        throw new Error(`User ${userId} not found`);
      }

      const userData = userDoc.data() as UserEntity;
      const now = admin.firestore.Timestamp.now();
      const currentTime = now.toDate();

      let newWithdrawnAmount = withdrawnAmount;
      let resetTime = now;

      // Check if user has existing withdrawal tracking
      if (userData.withdrawal_24h) {
        const lastResetTime = userData.withdrawal_24h.lastResetAt.toDate();
        const timeSinceReset = currentTime.getTime() - lastResetTime.getTime();

        // If less than 24 hours have passed, add to existing amount
        if (timeSinceReset < HOURS_24_IN_MS) {
          newWithdrawnAmount = userData.withdrawal_24h.amount + withdrawnAmount;
          resetTime = userData.withdrawal_24h.lastResetAt;
        }
        // If 24+ hours have passed, start fresh with new amount and reset time
      }

      const newWithdrawalTracking: Withdrawal24h = {
        amount: newWithdrawnAmount,
        lastResetAt: resetTime,
      };

      transaction.update(userRef, {
        withdrawal_24h: newWithdrawalTracking,
      });

      log.info("Withdrawal tracking updated", {
        userId,
        withdrawnAmount,
        newWithdrawnAmount,
        resetTime: resetTime.toDate(),
        operation: "update_withdrawal_tracking",
      });
    });
  } catch (error) {
    log.error("Error updating withdrawal tracking", error, {
      userId,
      withdrawnAmount,
      operation: "update_withdrawal_tracking",
    });
    throw error;
  }
}

/**
 * Gets the current withdrawal status for a user without making any changes
 */
export async function getWithdrawalStatus(
  userId: string,
  maxWithdrawalAmount: number
): Promise<WithdrawalLimitInfo> {
  return checkWithdrawalLimit({
    userId,
    requestedAmount: 0,
    maxWithdrawalAmount,
  });
}
