'use client';

import { format } from 'date-fns';
import { enUS, ru } from 'date-fns/locale';
import type { Timestamp } from 'firebase/firestore';

import { AppLocale } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';
import { firebaseTimestampToDate } from '@/utils/date-utils';

interface OrderDetailsDateProps {
  updatedAt?: Date | Timestamp;
  className?: string;
}

const localeMap = {
  [AppLocale.en]: enUS,
  [AppLocale.ru]: ru,
};

export function OrderDetailsDate({
  updatedAt,
  className,
}: OrderDetailsDateProps) {
  const { locale } = useRootContext();

  if (!updatedAt) {
    return null;
  }

  const date = firebaseTimestampToDate(updatedAt);
  const dateLocale = localeMap[locale] || enUS;

  const formattedDate = format(date, 'MMM d, yyyy HH:mm', {
    locale: dateLocale,
  });

  return (
    <div className={className}>
      <div className="flex justify-between items-center py-2">
        <span className="text-[#708499] text-sm">Last update</span>
        <span className="text-[#f5f5f5] text-sm font-medium">
          {formattedDate}
        </span>
      </div>
    </div>
  );
}
