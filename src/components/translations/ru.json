{"actions.apply": "Применить", "actions.cancel": "Отмена", "actions.confirm": "Подтвердить", "actions.delete": "Удалить", "actions.edit": "Редактировать", "actions.pause": "Пауза", "actions.reject": "Отклонить", "actions.resend": "Отправить повторно", "actions.save": "Сохранить", "actions.saved": "Сохранено", "actions.send": "Отправить", "actions.sent": "Отправлено", "actions.signIn": "Войти", "actions.signOut": "Выйти", "actions.signUp": "Зарегистрироваться", "actions.submit": "Отправить", "collectionName.unknownCollection": "Неизвестная коллекция", "countdownPopup.closeNotification": "Закрыть уведомление", "countdownPopup.depositProcessing": "Обработка депозита", "countdownPopup.minutes": "минут", "countdownPopup.youWillReceiveFundsWithin": "Вы получите средства в течение", "depositDrawer.actions.cancel": "Отмена", "depositDrawer.actions.deposit": "Депозит", "depositDrawer.actions.pleaseConnectWallet": "Пожалуйста, подключите кошелек для внесения депозита", "depositDrawer.actions.processing": "Обработка...", "depositDrawer.addTonToBalance": "Добавить TON на баланс маркетплейса", "depositDrawer.amountInput.amountMustBeAtLeast": "Сумма должна быть не менее {amount} TON", "depositDrawer.amountInput.depositAmountTon": "Сумма депозита (TON)", "depositDrawer.amountInput.minTonPlaceholder": "Мин {amount} TON", "depositDrawer.depositFee": "Комиссия депозита:", "depositDrawer.depositFunds": "Внести средства", "depositDrawer.depositInformation": "Информация о депозите", "depositDrawer.depositProcessing": "Обработка депозита", "depositDrawer.loadingConfiguration": "Загрузка конфигурации...", "depositDrawer.minimumDeposit": "Минимальный депозит:", "depositDrawer.summary.depositAmount": "Сумма депозита:", "depositDrawer.summary.depositFee": "Комиссия депозита:", "depositDrawer.summary.totalToPay": "Всего к оплате:", "depositDrawer.youWillReceiveFundsWithin": "Вы получите средства в течение", "errorPage.tryAgain": "Попробовать снова", "errorPage.unhandledErrorOccurred": "Произошла необработанная ошибка!", "errors.auth.adminOnly": "Только администраторы могут выполнить эту операцию.", "errors.auth.permissionDenied": "Доступ запрещен.", "errors.auth.permissionDeniedWithOperation": "Вы можете выполнить {operation} только для себя.", "errors.auth.tonWalletRequired": "У пользователя не настроен адрес кошелька TON.", "errors.auth.unauthenticated": "Требуется аутентификация.", "errors.auth.userNotFound": "Пользователь не найден.", "errors.generic.authenticationFailed": "Ошибка аутентификации.", "errors.generic.operationFailed": "Операция не удалась. Попробуйте снова.", "errors.generic.serverError": "Произошла ошибка сервера.", "errors.generic.unknownError": "Произошла неизвестная ошибка.", "errors.order.buyerCannotPurchaseSameOrder": "Вы не можете купить тот же заказ снова.", "errors.order.collectionNotActive": "Коллекция не активна.", "errors.order.collectionNotFound": "Коллекция не найдена.", "errors.order.insufficientBalance": "Недостаточно средств.", "errors.order.onlyBuyerCanSetSecondaryPrice": "Только текущий покупатель может установить цену на вторичном рынке.", "errors.order.onlyPaidOrdersPurchasable": "На вторичном рынке можно покупать только заказы со статусом ОПЛАЧЕН.", "errors.order.onlyPaidOrdersSecondaryMarket": "На вторичном рынке можно размещать только заказы со статусом ОПЛАЧЕН.", "errors.order.orderMustBeGiftSentStatus": "Заказ должен иметь статус 'подарок отправлен релейеру' для завершения покупки.", "errors.order.orderMustBePaidStatus": "Заказ должен иметь статус 'оплачен' для отправки подарка релейеру.", "errors.order.orderMustHaveBuyerAndSeller": "У заказа должны быть и покупатель, и продавец для размещения на вторичном рынке.", "errors.order.orderNotAvailableSecondaryMarket": "Заказ недоступен на вторичном рынке.", "errors.order.orderNotFound": "Заказ не найден.", "errors.order.secondaryPriceBelowMinimum": "Цена на вторичном рынке должна быть не менее {minPrice} TON.", "errors.order.secondaryPriceExceedsCollateral": "Цена на вторичном рынке не может превышать общий залог {totalCollateral} TON (покупатель: {buyerAmount} TON + продавец: {sellerAmount} TON).", "errors.order.sellerCannotPurchaseOwnOrder": "Продавец не может купить свой собственный заказ на вторичном рынке.", "errors.telegram.botTokenNotConfigured": "Токен Telegram бота не настроен.", "errors.telegram.firebaseAuthError": "Произошла ошибка Firebase Auth.", "errors.telegram.iamPermissionError": "У сервисного аккаунта Firebase нет необходимых разрешений IAM для создания пользовательских токенов.", "errors.telegram.initDataRequired": "Требуется initData.", "errors.telegram.invalidTelegramData": "Неверные данные Telegram.", "errors.validation.botTokenRequired": "Требуется токен бота.", "errors.validation.invalidBotToken": "Неверный токен бота.", "errors.validation.invalidCollectionId": "Требуется корректный ID коллекции.", "errors.validation.invalidOrderId": "Требуется корректный ID заказа.", "errors.validation.invalidPrice": "Требуется корректная цена.", "errors.validation.invalidSecondaryMarketPrice": "Требуется корректная цена для вторичного рынка.", "errors.validation.ownedGiftIdRequired": "Требуется ID принадлежащего подарка.", "errors.validation.positiveAmountRequired": "{fieldName} должно быть больше 0.", "errors.validation.requiredField": "{field} обязательно для заполнения.", "errors.validation.userIdOrTgIdRequired": "Требуется userId или tgId.", "errors.withdrawal.amountAboveMaximum": "Сумма вывода не может превышать {maxAmount} TON.", "errors.withdrawal.amountBelowMinimum": "Сумма вывода должна быть не менее {minAmount} TON.", "errors.withdrawal.amountExceeds24hLimit": "Сумма вывода превышает лимит за 24 часа. Вы можете вывести до {remainingLimit} TON. Лимит обновится в {resetAt}.", "errors.withdrawal.amountTooSmallAfterFees": "Сумма слишком мала после вычета комиссий.", "errors.withdrawal.insufficientAvailableBalance": "Недостаточно доступных средств для вывода.", "freezePeriodStatus.expired": "Истёк", "freezePeriodStatus.freezePeriodEnded": "Период заморозки завершён", "freezePeriodStatus.freezePeriodNotStarted": "Период заморозки ещё не начался", "freezePeriodStatus.timeRemaining": "Осталось {days}д {hours}ч {minutes}м {seconds}с", "giftInfoDrawer.claimGiftSteps": "Следуйте этим шагам, чтобы получить подарок от релейера", "giftInfoDrawer.claimYourGift": "Получить подарок", "giftInfoDrawer.sendGiftSteps": "Следуйте этим шагам, чтобы отправить подарок релейеру", "giftInfoDrawer.sendGiftToRelayer": "Отправить подарок релейеру", "header.deposit": "Депозит", "header.profile": "Профиль", "header.walletDisconnected": "Кошелек отключён", "header.withdraw": "Вывод", "loginModal.authenticationRequired": "Требуется аутентификация", "loginModal.mustBeLoggedIn": "Вы должны войти в систему для выполнения этого действия.", "loginModal.signInWithTelegram": "Войти через Telegram", "loginModal.signingIn": "Вход...", "marketplace.activity.executedOrdersDescription": "Выполненные заказы будут отображены здесь", "marketplace.activity.noActivityFound": "Активность не найдена", "marketplace.activity.orderNumber": "Заказ #{number}", "marketplace.activity.viewOrder": "Посмотреть заказ", "marketplace.tabs.activity": "Активность", "marketplace.tabs.buy": "Купить", "marketplace.tabs.sell": "Продать", "marketplaceFilters.allCollections": "Все коллекции", "marketplaceFilters.max": "<PERSON>а<PERSON><PERSON>", "marketplaceFilters.min": "<PERSON><PERSON><PERSON>", "marketplaceFilters.newestFirst": "Сначала новые", "marketplaceFilters.oldestFirst": "Сначала старые", "marketplaceFilters.priceHighToLow": "Цена: по убыванию", "marketplaceFilters.priceLowToHigh": "Цена: по возрастанию", "marketplaceFilters.sortBy": "Сортировать по", "mock.message": "Тестовое сообщение", "notFound.redirecting": "Перенаправление...", "notFound.takingYouBackToMarketplace": "Возвращаем вас на маркетплейс", "nouns.confirmation": "Подтверждение", "nouns.description": "Описание", "nouns.email": "Email", "nouns.error": "Ошибка", "nouns.from": "От", "nouns.name": "Имя", "nouns.password": "Пароль", "nouns.price": "Цена", "nouns.service": "Сервис", "orderActors.anonymousUser": "Анонимный пользователь", "orderActors.buyer": "Покупатель", "orderActors.noBuyerAssigned": "Покупатель не назначен", "orderActors.noRoleAssigned": "{role} не назначен", "orderActors.noSellerAssigned": "Продавец не назначен", "orderActors.resseller": "Перепродавец", "orderActors.seller": "Продавец", "orderDeadlineTimer.deadline": "Крайний срок", "orderDeadlineTimer.giftWillBecomeTransferable": "Подарок скоро станет передаваемым", "orderDeadlineTimer.waiting": "Ожидание", "orderDetails.content.action": "Действие", "orderDetails.content.buy": "Купить", "orderDetails.content.fulfill": "Выполнить", "orderDetails.content.openingTelegram": "Открытие Telegram...", "orderDetails.content.share": "Поделиться", "orderDetails.content.showResellHistory": "Показать историю перепродаж", "orderDetails.fees.buyer": "Покупатель", "orderDetails.fees.collateral": "Залог", "orderDetails.fees.collateralDescription": "{buyerPercentage}% залог для покупателей. Заблокирован до выполнения заказа. Мгновенно возвращается, если заказ не выполнен.", "orderDetails.fees.deposited": "Внесено", "orderDetails.fees.feePaidBySeller": "Комиссия {feePercent}%. Оплачивается продавцом.", "orderDetails.fees.orderDetailsAndFees": "Детали заказа и комиссии", "orderDetails.fees.purchaseFee": "Комиссия за покупку", "orderDetails.fees.seller": "Продавец", "orderStatus.active": "Активный", "orderStatus.cancelled": "Отменён", "orderStatus.fulfilled": "Выполнен", "orderStatus.giftSentToRelayer": "Отправлен боту", "orderStatus.paid": "Оплачен", "orders.cancelOrder.cancel": "Отменить", "orders.cancelOrder.cancelOrder": "Отменить заказ", "orders.cancelOrder.cancellationWarning": "Это действие нельзя отменить.", "orders.cancelOrder.cancelling": "Отмена...", "orders.cancelOrder.collateralLossWarning": "Вы потеряете {amount} TON залога.", "orders.cancelOrder.confirmCancellation": "Вы уверены, что хотите отменить этот заказ?", "orders.cancelOrder.failedToCancelOrder": "Не удалось отменить заказ: {message}", "orders.cancelOrder.keepOrder": "Оставить заказ", "orders.cancelOrder.orderCancelledSuccessfully": "Заказ успешно отменён", "orders.cancelOrder.resellerEarningsWarning": "Как перепродавец, вы потеряете потенциальную прибыль.", "orders.cancelOrder.unexpectedError": "Произошла неожиданная ошибка", "orders.clickLoginToSeeOrders": "Нажмите кнопку входа через Telegram, чтобы увидеть ваши заказы", "orders.noBuyOrdersFound": "Заказы на покупку не найдены", "orders.noSellOrdersFound": "Заказы на продажу не найдены", "orders.tabs.myBuyOrders": "Мои заказы на покупку ({count})", "orders.tabs.mySellOrders": "Мои заказы на продажу ({count})", "orders.userOrderCard.getAGift": "Получить подарок", "orders.userOrderCard.sendAGift": "Отправить подарок", "orders.youAreNotLoggedIn": "Вы не вошли в систему", "profile.form.displayName": "Отображаемое имя", "profile.form.editProfile": "Редактировать профиль", "profile.form.enterYourDisplayName": "Введите ваше отображаемое имя", "profile.form.failedToUpdateProfile": "Не удалось обновить профиль. Попробуйте снова.", "profile.form.nameIsRequired": "Имя обязательно", "profile.form.nameTooLong": "Имя должно быть меньше 50 символов", "profile.form.profileUpdatedSuccessfully": "Профиль успешно обновлён!", "profile.form.updateProfile": "Обновить профиль", "profile.form.updating": "Обновление...", "profile.main": "Главная", "profile.myTransactions": "Мои транзакции", "profile.referralSection.anonymous": "Анонимный", "profile.referralSection.failedToLoadReferrals": "Не удалось загрузить рефералов", "profile.referralSection.failedToShareReferralLink": "Не удалось поделиться реферальной ссылкой", "profile.referralSection.friends": "друзей", "profile.referralSection.joinMeOnMarketplace": "Присоединяйтесь ко мне на этом удивительном маркетплейсе и начните зарабатывать вознаграждения!", "profile.referralSection.joinTheMarketplace": "Присоединиться к маркетплейсу", "profile.referralSection.loadingReferralData": "Загрузка реферальных данных...", "profile.referralSection.name": "Имя", "profile.referralSection.ofTheirPurchaseFees": "от их комиссий за покупку", "profile.referralSection.points": "<PERSON><PERSON><PERSON><PERSON>", "profile.referralSection.potentialEarnings": "Потенциальный доход", "profile.referralSection.referralLinkCopiedToClipboard": "Реферальная ссылка скопирована в буфер обмена!", "profile.referralSection.referralLinkSharedSuccessfully": "Реферальная ссылка успешно отправлена!", "profile.referralSection.referralProgram": "Реферальная программа", "profile.referralSection.referralRateDescription": "Вы зарабатываете {percentage}% от комиссии за покупку, когда ваши рефералы совершают покупки", "profile.referralSection.shareReferralLink": "Поделиться реферальной ссылкой", "profile.referralSection.shareTheLinkGetPoints": "Поделитесь ссылкой - получите очки за подарки!", "profile.referralSection.sharing": "Отправка...", "profile.referralSection.yourReferralRate": "Ваша реферальная ставка", "profile.referralSection.yourReferrals": "Ваши рефералы ({count})", "profile.settings.animatedCollections": "Анимированные коллекции", "profile.settings.animatedCollectionsDescription": "Включить анимированные превью коллекций и эффекты", "profile.settings.settings": "Настройки", "profile.socialLinks.followUs": "Подписывайтесь на нас", "profile.socialLinks.followUsOn": "Подписывайтесь на нас в {platform}", "profile.transactionHistory.emptyState.noTransactionsYet": "Пока нет транзакций", "profile.transactionHistory.emptyState.transactionHistoryDescription": "История ваших транзакций появится здесь, когда вы начнёте торговать на маркетплейсе", "profile.transactionHistory.header.beta": "БЕТА", "profile.transactionHistory.header.refresh": "Обновить", "profile.transactionHistory.header.transactionHistory": "История транзакций", "profile.transactionHistory.loadingState.loadingYourTransactions": "Загрузка ваших транзакций...", "profile.transactionHistory.pagination.loadingMoreTransactions": "Загрузка дополнительных транзакций...", "profile.transactionHistory.pagination.reachedEndOfHistory": "Вы достигли конца истории транзакций", "profile.transactionHistory.table.amount": "Сумма", "profile.transactionHistory.table.date": "Дата", "profile.transactionHistory.table.description": "Описание", "profile.transactionHistory.table.type": "Тип", "profile.transactionHistory.transactionHistory": "История транзакций", "profile.userInfo.anonymousUser": "Анонимный пользователь", "profile.userInfo.availableBalance": "Доступный баланс", "profile.userInfo.lockedBalance": "Заблокированный баланс", "profile.userInfo.myPoints": "Мои очки", "profile.userInfo.profileInformation": "Информация профиля", "profile.userInfo.totalBalance": "О<PERSON><PERSON><PERSON> баланс", "secondaryMarketBadge.resell": "Перепродажа", "sellButtonComponent.buy": "Купить", "tonConnect.authenticating": "Аутентификация...", "tonConnect.connect": "Подключить", "tonConnect.connecting": "Подключение...", "tonConnect.disconnect": "Отключить", "withdrawDrawer.amountMustBeAtLeast": "Сумма должна быть не менее 1 TON", "withdrawDrawer.availableBalance": "Доступный баланс:", "withdrawDrawer.cancel": "Отмена", "withdrawDrawer.enterAmountToWithdraw": "Введите сумму для вывода", "withdrawDrawer.insufficientAvailableBalance": "Недостаточно доступных средств", "withdrawDrawer.insufficientBalance": "Недостаточно доступных средств", "withdrawDrawer.invalidAmount": "Неверная сумма", "withdrawDrawer.invalidWithdrawalAmount": "Неверная сумма для вывода", "withdrawDrawer.limitResetsAt": "Лимит обновится в:", "withdrawDrawer.loadingConfiguration": "Загрузка конфигурации...", "withdrawDrawer.minTonPlaceholder": "Мин 1 TON", "withdrawDrawer.minimumWithdrawal": "Минимальный вывод:", "withdrawDrawer.minimumWithdrawalAmount": "Минимальная сумма вывода 1 TON", "withdrawDrawer.netAmount": "Чистая сумма:", "withdrawDrawer.noWalletAddressFound": "Адрес кошелька не найден в вашем профиле", "withdrawDrawer.pleaseConnectWallet": "Пожалуйста, подключите кошелек для вывода средств", "withdrawDrawer.pleaseConnectWalletFirst": "Сначала подключите ваш кошелек", "withdrawDrawer.processing": "Обработка...", "withdrawDrawer.remainingLimit": "Оставшийся лимит:", "withdrawDrawer.unexpectedError": "Произошла неожиданная ошибка", "withdrawDrawer.withdraw": "Вывести", "withdrawDrawer.withdrawAmount": "Сумма вывода:", "withdrawDrawer.withdrawAmountTon": "Сумма вывода (TON)", "withdrawDrawer.withdrawFunds": "Вывести средства", "withdrawDrawer.withdrawTonToWallet": "Вывести TON на подключённый кошелек", "withdrawDrawer.withdrawalFailed": "Вывод не удался: {message}", "withdrawDrawer.withdrawalFee": "Комиссия за вывод:", "withdrawDrawer.withdrawalInformation": "Информация о выводе", "withdrawDrawer.withdrawalLimit24h": "Лимит вывода за 24 часа:", "withdrawDrawer.withdrawalSuccessful": "Вывод успешен! Транзакция: {hash}", "withdrawDrawer.youWillReceive": "Вы получите:"}