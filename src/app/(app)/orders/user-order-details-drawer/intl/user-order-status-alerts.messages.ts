import { defineMessages } from 'react-intl';

export const userOrderStatusAlertsMessages = defineMessages({
  freezePeriodActive: {
    id: 'userOrderStatusAlerts.freezePeriodActive',
    defaultMessage: 'Freeze Period Active',
  },
  freezePeriodDescription: {
    id: 'userOrderStatusAlerts.freezePeriodDescription',
    defaultMessage: 'Collection items cannot be transferred yet. Wait for the freeze period to end.',
  },
  waitingForTransfer: {
    id: 'userOrderStatusAlerts.waitingForTransfer',
    defaultMessage: 'Waiting for Transfer',
  },
  waitingForTransferDescription: {
    id: 'userOrderStatusAlerts.waitingForTransferDescription',
    defaultMessage: 'Wait until the collection item becomes transferable.',
  },
  readyToSend: {
    id: 'userOrderStatusAlerts.readyToSend',
    defaultMessage: 'Ready to Send',
  },
  readyToSendDescription: {
    id: 'userOrderStatusAlerts.readyToSendDescription',
    defaultMessage: 'You can now send the gift to the relayer.',
  },
});
