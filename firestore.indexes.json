{"indexes": [{"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "buyerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "buyerId", "order": "ASCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "sellerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "buyerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "buyerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "sellerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "buyerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "sellerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "deadline", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "ASCENDING"}, {"fieldPath": "sellerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "sellerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "sellerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "price", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "price", "order": "DESCENDING"}, {"fieldPath": "sellerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "collectionId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "sellerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sellerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sellerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sellerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "ASCENDING"}, {"fieldPath": "sellerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "buyerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "sellerId", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "buyerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "secondaryMarketPrice", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "sellerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "deadline", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "price", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "price", "order": "DESCENDING"}, {"fieldPath": "sellerId", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}